import * as admin from 'firebase-admin';
import { ServiceAccount } from 'firebase-admin';

interface FirebaseConfig {
    projectId: string;
    privateKey: string;
    clientEmail: string;
    databaseURL?: string;
}

const getFirebaseConfig = (): FirebaseConfig => {
    const requiredEnvVars = [
        'FIREBASE_PROJECT_ID',
        'FIREBASE_PRIVATE_KEY',
        'FIREBASE_CLIENT_EMAIL'
    ];

    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
    
    if (missingVars.length > 0) {
        throw new Error(`Missing required Firebase environment variables: ${missingVars.join(', ')}`);
    }

    return {
        projectId: process.env.FIREBASE_PROJECT_ID!,
        privateKey: process.env.FIREBASE_PRIVATE_KEY!.replace(/\\n/g, '\n'),
        clientEmail: process.env.FIREBASE_CLIENT_EMAIL!,
        databaseURL: process.env.FIREBASE_DATABASE_URL
    };
};

let firebaseApp: admin.app.App | null = null;

export const initializeFirebase = (): admin.app.App => {
    if (firebaseApp) {
        return firebaseApp;
    }

    try {
        const config = getFirebaseConfig();
        
        const serviceAccount: ServiceAccount = {
            projectId: config.projectId,
            privateKey: config.privateKey,
            clientEmail: config.clientEmail
        };

        firebaseApp = admin.initializeApp({
            credential: admin.credential.cert(serviceAccount),
            databaseURL: config.databaseURL
        });

        console.log('Firebase Admin SDK initialized successfully');
        return firebaseApp;
        
    } catch (error) {
        console.error('Failed to initialize Firebase Admin SDK:', error);
        throw error;
    }
};

export const getFirebaseApp = (): admin.app.App => {
    if (!firebaseApp) {
        return initializeFirebase();
    }
    return firebaseApp;
};

export const getMessaging = (): admin.messaging.Messaging => {
    const app = getFirebaseApp();
    return admin.messaging(app);
};
