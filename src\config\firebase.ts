import * as admin from 'firebase-admin';
import { ServiceAccount } from 'firebase-admin';
import * as path from 'path';
import * as fs from 'fs';

interface FirebaseConfig {
    projectId: string;
    privateKey: string;
    clientEmail: string;
    databaseURL?: string;
}

const getFirebaseConfigFromEnv = (): FirebaseConfig => {
    const requiredEnvVars = [
        'FIREBASE_PROJECT_ID',
        'FIREBASE_PRIVATE_KEY',
        'FIREBASE_CLIENT_EMAIL'
    ];

    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

    if (missingVars.length > 0) {
        throw new Error(`Missing required Firebase environment variables: ${missingVars.join(', ')}`);
    }

    return {
        projectId: process.env.FIREBASE_PROJECT_ID!,
        privateKey: process.env.FIREBASE_PRIVATE_KEY!.replace(/\\n/g, '\n'),
        clientEmail: process.env.FIREBASE_CLIENT_EMAIL!,
        databaseURL: process.env.FIREBASE_DATABASE_URL
    };
};

const getFirebaseConfigFromFile = (filePath: string): ServiceAccount => {
    if (!fs.existsSync(filePath)) {
        throw new Error(`Firebase service account file not found at: ${filePath}`);
    }

    try {
        const serviceAccountData = JSON.parse(fs.readFileSync(filePath, 'utf8'));

        // Validate required fields
        const requiredFields = ['project_id', 'private_key', 'client_email'];
        const missingFields = requiredFields.filter(field => !serviceAccountData[field]);

        if (missingFields.length > 0) {
            throw new Error(`Missing required fields in service account file: ${missingFields.join(', ')}`);
        }

        return serviceAccountData as ServiceAccount;
    } catch (error) {
        if (error instanceof SyntaxError) {
            throw new Error(`Invalid JSON in Firebase service account file: ${filePath}`);
        }
        throw error;
    }
};

let firebaseApp: admin.app.App | null = null;

export const initializeFirebase = (): admin.app.App => {
    if (firebaseApp) {
        return firebaseApp;
    }

    try {
        let credential: admin.credential.Credential;
        let databaseURL: string | undefined;

        // Method 1: Try to use service account JSON file path
        if (process.env.FIREBASE_SERVICE_ACCOUNT_PATH) {
            const serviceAccountPath = path.resolve(process.env.FIREBASE_SERVICE_ACCOUNT_PATH);
            console.log(`Loading Firebase config from file: ${serviceAccountPath}`);

            const serviceAccount = getFirebaseConfigFromFile(serviceAccountPath);
            credential = admin.credential.cert(serviceAccount);
            databaseURL = process.env.FIREBASE_DATABASE_URL;
        }
        // Method 2: Try to use service account JSON content directly
        else if (process.env.FIREBASE_SERVICE_ACCOUNT_JSON) {
            console.log('Loading Firebase config from JSON environment variable');

            try {
                const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_JSON) as ServiceAccount;
                credential = admin.credential.cert(serviceAccount);
                databaseURL = process.env.FIREBASE_DATABASE_URL;
            } catch (error) {
                throw new Error('Invalid JSON in FIREBASE_SERVICE_ACCOUNT_JSON environment variable');
            }
        }
        // Method 3: Use individual environment variables (legacy method)
        else {
            console.log('Loading Firebase config from individual environment variables');

            const config = getFirebaseConfigFromEnv();
            const serviceAccount: ServiceAccount = {
                projectId: config.projectId,
                privateKey: config.privateKey,
                clientEmail: config.clientEmail
            };
            credential = admin.credential.cert(serviceAccount);
            databaseURL = config.databaseURL;
        }

        firebaseApp = admin.initializeApp({
            credential: credential,
            databaseURL: databaseURL
        });

        console.log('Firebase Admin SDK initialized successfully');
        return firebaseApp;

    } catch (error) {
        console.error('Failed to initialize Firebase Admin SDK:', error);
        throw error;
    }
};

export const getFirebaseApp = (): admin.app.App => {
    if (!firebaseApp) {
        return initializeFirebase();
    }
    return firebaseApp;
};

export const getMessaging = (): admin.messaging.Messaging => {
    const app = getFirebaseApp();
    return admin.messaging(app);
};
