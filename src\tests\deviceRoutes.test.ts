import request from 'supertest';
import express from 'express';
import deviceRoutes from '../routes/deviceRoutes';
import { FCMService } from '../services/fcmService';
import { Pool } from 'pg';

// Mock dependencies
jest.mock('../services/fcmService');
jest.mock('pg');
jest.mock('../middleware/auth', () => ({
    authMiddleware: (req: any, res: any, next: any) => {
        req.userData = { id: 'user123' };
        next();
    }
}));

const app = express();
app.use(express.json());
app.use('/api/devices', deviceRoutes);

describe('Device Routes', () => {
    let mockFCMService: jest.Mocked<FCMService>;
    let mockPool: jest.Mocked<Pool>;

    beforeEach(() => {
        jest.clearAllMocks();
        
        mockFCMService = {
            registerFCMToken: jest.fn(),
            removeFCMToken: jest.fn(),
            getFCMTokensForUser: jest.fn(),
            sendMessageNotification: jest.fn(),
            sendMessageNotificationToUsers: jest.fn(),
            sendNotificationToTokens: jest.fn()
        } as any;

        (FCMService.getInstance as jest.Mock).mockReturnValue(mockFCMService);

        mockPool = {
            query: jest.fn()
        } as any;

        (Pool as jest.Mock).mockImplementation(() => mockPool);
    });

    describe('POST /api/devices/fcm-token', () => {
        it('should register FCM token successfully', async () => {
            const requestBody = {
                fcm_token: 'test_token',
                device_id: 'device123',
                user_app_version: '1.0.0'
            };

            // Mock user lookup
            mockPool.query.mockResolvedValueOnce({
                rows: [{ user_reference: 'U123456' }]
            } as any);

            mockFCMService.registerFCMToken.mockResolvedValueOnce(true);

            const response = await request(app)
                .post('/api/devices/fcm-token')
                .send(requestBody);

            expect(response.status).toBe(200);
            expect(response.body.success).toBe(true);
            expect(response.body.message).toBe('FCM token registered successfully');
            expect(mockFCMService.registerFCMToken).toHaveBeenCalledWith(
                'U123456',
                'test_token',
                'device123',
                '1.0.0'
            );
        });

        it('should return 400 when FCM token is missing', async () => {
            const response = await request(app)
                .post('/api/devices/fcm-token')
                .send({});

            expect(response.status).toBe(400);
            expect(response.body.success).toBe(false);
            expect(response.body.message).toBe('FCM token is required');
        });

        it('should return 404 when user not found', async () => {
            const requestBody = {
                fcm_token: 'test_token'
            };

            // Mock user not found
            mockPool.query.mockResolvedValueOnce({
                rows: []
            } as any);

            const response = await request(app)
                .post('/api/devices/fcm-token')
                .send(requestBody);

            expect(response.status).toBe(404);
            expect(response.body.success).toBe(false);
            expect(response.body.message).toBe('User not found');
        });

        it('should return 500 when FCM service fails', async () => {
            const requestBody = {
                fcm_token: 'test_token'
            };

            // Mock user lookup
            mockPool.query.mockResolvedValueOnce({
                rows: [{ user_reference: 'U123456' }]
            } as any);

            mockFCMService.registerFCMToken.mockResolvedValueOnce(false);

            const response = await request(app)
                .post('/api/devices/fcm-token')
                .send(requestBody);

            expect(response.status).toBe(500);
            expect(response.body.success).toBe(false);
            expect(response.body.message).toBe('Failed to register FCM token');
        });
    });

    describe('DELETE /api/devices/fcm-token', () => {
        it('should remove FCM token successfully', async () => {
            const requestBody = {
                fcm_token: 'test_token'
            };

            // Mock user lookup
            mockPool.query.mockResolvedValueOnce({
                rows: [{ user_reference: 'U123456' }]
            } as any);

            mockFCMService.removeFCMToken.mockResolvedValueOnce(true);

            const response = await request(app)
                .delete('/api/devices/fcm-token')
                .send(requestBody);

            expect(response.status).toBe(200);
            expect(response.body.success).toBe(true);
            expect(response.body.message).toBe('FCM token removed successfully');
            expect(mockFCMService.removeFCMToken).toHaveBeenCalledWith('U123456', 'test_token');
        });

        it('should return 400 when FCM token is missing', async () => {
            const response = await request(app)
                .delete('/api/devices/fcm-token')
                .send({});

            expect(response.status).toBe(400);
            expect(response.body.success).toBe(false);
            expect(response.body.message).toBe('FCM token is required');
        });
    });

    describe('GET /api/devices', () => {
        it('should return user devices successfully', async () => {
            const mockDevices = [
                {
                    user_device_details_id: 1,
                    device_id: 'device123',
                    fcm_token: 'token123',
                    user_app_version: '1.0.0',
                    created_at: new Date(),
                    updated_at: new Date()
                }
            ];

            // Mock user lookup
            mockPool.query.mockResolvedValueOnce({
                rows: [{ user_reference: 'U123456' }]
            } as any);

            // Mock devices lookup
            mockPool.query.mockResolvedValueOnce({
                rows: mockDevices
            } as any);

            const response = await request(app)
                .get('/api/devices');

            expect(response.status).toBe(200);
            expect(response.body.success).toBe(true);
            expect(response.body.data).toEqual(mockDevices);
        });

        it('should return 404 when user not found', async () => {
            // Mock user not found
            mockPool.query.mockResolvedValueOnce({
                rows: []
            } as any);

            const response = await request(app)
                .get('/api/devices');

            expect(response.status).toBe(404);
            expect(response.body.success).toBe(false);
            expect(response.body.message).toBe('User not found');
        });
    });

    describe('PUT /api/devices/:deviceId', () => {
        it('should update device successfully', async () => {
            const deviceId = 'device123';
            const requestBody = {
                fcm_token: 'new_token',
                user_app_version: '2.0.0'
            };

            const mockUpdatedDevice = {
                user_device_details_id: 1,
                device_id: deviceId,
                fcm_token: 'new_token',
                user_app_version: '2.0.0',
                created_at: new Date(),
                updated_at: new Date()
            };

            // Mock user lookup
            mockPool.query.mockResolvedValueOnce({
                rows: [{ user_reference: 'U123456' }]
            } as any);

            // Mock device update
            mockPool.query.mockResolvedValueOnce({
                rows: [mockUpdatedDevice]
            } as any);

            const response = await request(app)
                .put(`/api/devices/${deviceId}`)
                .send(requestBody);

            expect(response.status).toBe(200);
            expect(response.body.success).toBe(true);
            expect(response.body.message).toBe('Device updated successfully');
            expect(response.body.data.fcm_token).toBe('new_token');
        });

        it('should return 404 when device not found', async () => {
            const deviceId = 'device123';
            const requestBody = {
                fcm_token: 'new_token'
            };

            // Mock user lookup
            mockPool.query.mockResolvedValueOnce({
                rows: [{ user_reference: 'U123456' }]
            } as any);

            // Mock device not found
            mockPool.query.mockResolvedValueOnce({
                rows: []
            } as any);

            const response = await request(app)
                .put(`/api/devices/${deviceId}`)
                .send(requestBody);

            expect(response.status).toBe(404);
            expect(response.body.success).toBe(false);
            expect(response.body.message).toBe('Device not found');
        });
    });

    describe('DELETE /api/devices/:deviceId', () => {
        it('should delete device successfully', async () => {
            const deviceId = 'device123';

            // Mock user lookup
            mockPool.query.mockResolvedValueOnce({
                rows: [{ user_reference: 'U123456' }]
            } as any);

            // Mock device deletion
            mockPool.query.mockResolvedValueOnce({
                rows: [{ device_id: deviceId }]
            } as any);

            const response = await request(app)
                .delete(`/api/devices/${deviceId}`);

            expect(response.status).toBe(200);
            expect(response.body.success).toBe(true);
            expect(response.body.message).toBe('Device deleted successfully');
        });

        it('should return 404 when device not found', async () => {
            const deviceId = 'device123';

            // Mock user lookup
            mockPool.query.mockResolvedValueOnce({
                rows: [{ user_reference: 'U123456' }]
            } as any);

            // Mock device not found
            mockPool.query.mockResolvedValueOnce({
                rows: []
            } as any);

            const response = await request(app)
                .delete(`/api/devices/${deviceId}`);

            expect(response.status).toBe(404);
            expect(response.body.success).toBe(false);
            expect(response.body.message).toBe('Device not found');
        });
    });
});
