# Firebase Configuration Examples
# Choose ONE of the following methods:

# ===== METHOD 1: Service Account JSON File (Recommended) =====
FIREBASE_SERVICE_ACCOUNT_PATH=./config/firebase-service-account.json
FIREBASE_DATABASE_URL=https://your-project-id-default-rtdb.firebaseio.com/

# ===== METHOD 2: Service Account JSON as Environment Variable =====
# FIREBASE_SERVICE_ACCOUNT_JSON={"type":"service_account","project_id":"your-project-id",...}
# FIREBASE_DATABASE_URL=https://your-project-id-default-rtdb.firebaseio.com/

# ===== METHOD 3: Individual Environment Variables (Legacy) =====
# FIREBASE_PROJECT_ID=your-project-id
# FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour private key here\n-----END PRIVATE KEY-----\n"
# FIREBASE_CLIENT_EMAIL=<EMAIL>
# FIREBASE_DATABASE_URL=https://your-project-id-default-rtdb.firebaseio.com/

# Note: Only use ONE method above. The system will try them in order: 1 -> 2 -> 3
