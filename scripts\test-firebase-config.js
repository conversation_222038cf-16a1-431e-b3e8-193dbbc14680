#!/usr/bin/env node

/**
 * Test script to verify Firebase configuration
 * Run with: node scripts/test-firebase-config.js
 */

require('dotenv').config();

async function testFirebaseConfig() {
    console.log('🔥 Testing Firebase Configuration...\n');

    try {
        // Import Firebase config
        const { initializeFirebase } = require('../dist/config/firebase');
        
        console.log('📋 Configuration Method Detection:');
        
        if (process.env.FIREBASE_SERVICE_ACCOUNT_PATH) {
            console.log('✅ Method 1: Service Account JSON File Path');
            console.log(`   Path: ${process.env.FIREBASE_SERVICE_ACCOUNT_PATH}`);
        } else if (process.env.FIREBASE_SERVICE_ACCOUNT_JSON) {
            console.log('✅ Method 2: Service Account JSON Environment Variable');
            console.log('   JSON content detected in environment variable');
        } else if (process.env.FIREBASE_PROJECT_ID && process.env.FIREBASE_PRIVATE_KEY && process.env.FIREBASE_CLIENT_EMAIL) {
            console.log('✅ Method 3: Individual Environment Variables');
            console.log(`   Project ID: ${process.env.FIREBASE_PROJECT_ID}`);
            console.log(`   Client Email: ${process.env.FIREBASE_CLIENT_EMAIL}`);
        } else {
            console.log('❌ No Firebase configuration found!');
            console.log('\nPlease set up one of the following:');
            console.log('1. FIREBASE_SERVICE_ACCOUNT_PATH (recommended)');
            console.log('2. FIREBASE_SERVICE_ACCOUNT_JSON');
            console.log('3. FIREBASE_PROJECT_ID, FIREBASE_PRIVATE_KEY, FIREBASE_CLIENT_EMAIL');
            process.exit(1);
        }

        console.log('\n🚀 Initializing Firebase...');
        
        // Initialize Firebase
        const app = initializeFirebase();
        
        console.log('✅ Firebase initialized successfully!');
        console.log(`   App Name: ${app.name}`);
        
        // Test messaging service
        const { getMessaging } = require('../dist/config/firebase');
        const messaging = getMessaging();
        
        console.log('✅ Firebase Messaging service ready!');
        
        console.log('\n🎉 All tests passed! Firebase is configured correctly.');
        
    } catch (error) {
        console.error('❌ Firebase configuration test failed:');
        console.error(error.message);
        
        if (error.message.includes('ENOENT')) {
            console.log('\n💡 Tip: Make sure the service account file path is correct');
        } else if (error.message.includes('Invalid JSON')) {
            console.log('\n💡 Tip: Check that your JSON file or environment variable contains valid JSON');
        } else if (error.message.includes('Missing required fields')) {
            console.log('\n💡 Tip: Make sure your service account file contains all required fields');
        }
        
        process.exit(1);
    }
}

// Check if TypeScript files are compiled
const fs = require('fs');
const path = require('path');

const distPath = path.join(__dirname, '../dist/config/firebase.js');
if (!fs.existsSync(distPath)) {
    console.log('❌ TypeScript files not compiled. Please run: npm run build');
    process.exit(1);
}

testFirebaseConfig();
