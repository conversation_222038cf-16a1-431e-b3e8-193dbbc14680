import { FCMService, MessageNotificationData } from '../services/fcmService';
import { Pool } from 'pg';

// Mock Firebase Admin
jest.mock('firebase-admin', () => ({
    messaging: jest.fn(() => ({
        sendMulticast: jest.fn()
    })),
    credential: {
        cert: jest.fn()
    },
    initializeApp: jest.fn()
}));

// Mock the Firebase config
jest.mock('../config/firebase', () => ({
    getMessaging: jest.fn(() => ({
        sendMulticast: jest.fn()
    }))
}));

// Mock Pool
jest.mock('pg', () => ({
    Pool: jest.fn(() => ({
        query: jest.fn()
    }))
}));

describe('FCMService', () => {
    let fcmService: FCMService;
    let mockPool: jest.Mocked<Pool>;
    let mockMessaging: any;

    beforeEach(() => {
        // Reset all mocks
        jest.clearAllMocks();
        
        // Get the mocked Pool instance
        mockPool = new Pool() as jest.Mocked<Pool>;
        
        // Mock the messaging service
        mockMessaging = {
            sendMulticast: jest.fn()
        };
        
        // Mock the getMessaging function
        const { getMessaging } = require('../config/firebase');
        getMessaging.mockReturnValue(mockMessaging);
        
        fcmService = FCMService.getInstance();
    });

    describe('getFCMTokensForUser', () => {
        it('should return FCM tokens for a user', async () => {
            const userReference = 'U123456';
            const mockTokens = [
                { fcm_token: 'token1' },
                { fcm_token: 'token2' }
            ];

            mockPool.query.mockResolvedValueOnce({
                rows: mockTokens
            } as any);

            const result = await fcmService.getFCMTokensForUser(userReference);

            expect(result).toEqual(['token1', 'token2']);
            expect(mockPool.query).toHaveBeenCalledWith(
                expect.stringContaining('SELECT fcm_token'),
                [userReference]
            );
        });

        it('should return empty array when no tokens found', async () => {
            const userReference = 'U123456';

            mockPool.query.mockResolvedValueOnce({
                rows: []
            } as any);

            const result = await fcmService.getFCMTokensForUser(userReference);

            expect(result).toEqual([]);
        });

        it('should handle database errors gracefully', async () => {
            const userReference = 'U123456';

            mockPool.query.mockRejectedValueOnce(new Error('Database error'));

            const result = await fcmService.getFCMTokensForUser(userReference);

            expect(result).toEqual([]);
        });
    });

    describe('sendNotificationToTokens', () => {
        it('should send notifications successfully', async () => {
            const tokens = ['token1', 'token2'];
            const payload = {
                title: 'Test Title',
                body: 'Test Body',
                data: { key: 'value' }
            };

            mockMessaging.sendMulticast.mockResolvedValueOnce({
                successCount: 2,
                failureCount: 0,
                responses: [
                    { success: true },
                    { success: true }
                ]
            });

            const result = await fcmService.sendNotificationToTokens(tokens, payload);

            expect(result.successCount).toBe(2);
            expect(result.failureCount).toBe(0);
            expect(result.failedTokens).toEqual([]);
            expect(mockMessaging.sendMulticast).toHaveBeenCalledWith({
                notification: {
                    title: payload.title,
                    body: payload.body,
                    imageUrl: undefined
                },
                data: payload.data,
                tokens: tokens
            });
        });

        it('should handle failed tokens', async () => {
            const tokens = ['token1', 'token2'];
            const payload = {
                title: 'Test Title',
                body: 'Test Body'
            };

            mockMessaging.sendMulticast.mockResolvedValueOnce({
                successCount: 1,
                failureCount: 1,
                responses: [
                    { success: true },
                    { success: false, error: new Error('Invalid token') }
                ]
            });

            // Mock the cleanup query
            mockPool.query.mockResolvedValueOnce({} as any);

            const result = await fcmService.sendNotificationToTokens(tokens, payload);

            expect(result.successCount).toBe(1);
            expect(result.failureCount).toBe(1);
            expect(result.failedTokens).toEqual(['token2']);
        });

        it('should return zero counts for empty token array', async () => {
            const result = await fcmService.sendNotificationToTokens([], {
                title: 'Test',
                body: 'Test'
            });

            expect(result.successCount).toBe(0);
            expect(result.failureCount).toBe(0);
            expect(result.failedTokens).toEqual([]);
        });
    });

    describe('sendMessageNotification', () => {
        it('should send message notification successfully', async () => {
            const userReference = 'U123456';
            const messageData: MessageNotificationData = {
                chatId: 'chat123',
                messageId: 'msg123',
                senderName: 'John Doe',
                messageContent: 'Hello World',
                messageType: 'text'
            };

            // Mock getting FCM tokens
            mockPool.query.mockResolvedValueOnce({
                rows: [{ fcm_token: 'token1' }]
            } as any);

            // Mock successful notification send
            mockMessaging.sendMulticast.mockResolvedValueOnce({
                successCount: 1,
                failureCount: 0,
                responses: [{ success: true }]
            });

            const result = await fcmService.sendMessageNotification(userReference, messageData);

            expect(result).toBe(true);
        });

        it('should return false when no FCM tokens found', async () => {
            const userReference = 'U123456';
            const messageData: MessageNotificationData = {
                chatId: 'chat123',
                messageId: 'msg123',
                senderName: 'John Doe',
                messageContent: 'Hello World',
                messageType: 'text'
            };

            // Mock no FCM tokens
            mockPool.query.mockResolvedValueOnce({
                rows: []
            } as any);

            const result = await fcmService.sendMessageNotification(userReference, messageData);

            expect(result).toBe(false);
        });
    });

    describe('registerFCMToken', () => {
        it('should register new FCM token', async () => {
            const userReference = 'U123456';
            const fcmToken = 'new_token';
            const deviceId = 'device123';

            // Mock no existing device
            mockPool.query.mockResolvedValueOnce({
                rows: []
            } as any);

            // Mock successful insert
            mockPool.query.mockResolvedValueOnce({} as any);

            const result = await fcmService.registerFCMToken(userReference, fcmToken, deviceId);

            expect(result).toBe(true);
            expect(mockPool.query).toHaveBeenCalledTimes(2);
        });

        it('should update existing FCM token', async () => {
            const userReference = 'U123456';
            const fcmToken = 'updated_token';
            const deviceId = 'device123';

            // Mock existing device
            mockPool.query.mockResolvedValueOnce({
                rows: [{ user_device_details_id: 1 }]
            } as any);

            // Mock successful update
            mockPool.query.mockResolvedValueOnce({} as any);

            const result = await fcmService.registerFCMToken(userReference, fcmToken, deviceId);

            expect(result).toBe(true);
            expect(mockPool.query).toHaveBeenCalledTimes(2);
        });
    });

    describe('removeFCMToken', () => {
        it('should remove FCM token successfully', async () => {
            const userReference = 'U123456';
            const fcmToken = 'token_to_remove';

            mockPool.query.mockResolvedValueOnce({} as any);

            const result = await fcmService.removeFCMToken(userReference, fcmToken);

            expect(result).toBe(true);
            expect(mockPool.query).toHaveBeenCalledWith(
                expect.stringContaining('DELETE FROM user_device_details'),
                [userReference, fcmToken]
            );
        });

        it('should handle database errors', async () => {
            const userReference = 'U123456';
            const fcmToken = 'token_to_remove';

            mockPool.query.mockRejectedValueOnce(new Error('Database error'));

            const result = await fcmService.removeFCMToken(userReference, fcmToken);

            expect(result).toBe(false);
        });
    });
});
