import express from 'express';
import dotenv from 'dotenv';
dotenv.config();
import { config } from './config/env';
import routes from './routes';
import { WebSocketService } from './services/websocketService';
import { RabbitMQService } from './services/rabbitmqService';
import { initializeFirebase } from './config/firebase';
import http from 'http';
import { IncomingMessage } from 'http';
import { Duplex } from 'stream';
import { setupMiddleware } from './middleware';

const app = express();

// Setup middleware
setupMiddleware(app);

// Use the routes
app.use('/api', routes);

const server = http.createServer(app);

// Initialize WebSocket service
const wsService = WebSocketService.getInstance();

// Initialize Firebase
try {
    initializeFirebase();
    console.log('Firebase initialized successfully');
} catch (error) {
    console.error('Failed to initialize Firebase:', error);
    // Don't exit - FCM is not critical for basic functionality
}

// Initialize RabbitMQ service
const rabbitMQService = RabbitMQService.getInstance();
rabbitMQService.initialize().catch(error => {
    console.error('Failed to initialize RabbitMQ:', error);
    process.exit(1);  // Exit if RabbitMQ initialization fails
});

// Handle WebSocket upgrade
server.on('upgrade', (request: IncomingMessage, socket: Duplex, head: Buffer) => {
    wsService.handleUpgrade(request, socket as any, head);
});

const PORT = config.server.port;
const WS_PORT = config.server.wsPort;

// Start HTTP server
server.listen(PORT, () => {
    console.log(`HTTP server is running on http://localhost:${PORT}`);
});

// Start WebSocket server
const wsServer = http.createServer();
wsServer.on('upgrade', (request: IncomingMessage, socket: Duplex, head: Buffer) => {
    wsService.handleUpgrade(request, socket as any, head);
});

wsServer.listen(WS_PORT, () => {
    console.log(`WebSocket server is running on ws://localhost:${WS_PORT}`);
});