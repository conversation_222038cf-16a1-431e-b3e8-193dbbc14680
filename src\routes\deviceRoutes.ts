import { Router, Request, Response } from 'express';
import { FCMService } from '../services/fcmService';
import { Pool } from 'pg';
import { config } from '../config/env';
import { authMiddleware } from '../middleware/auth';
import { UserData } from '../interfaces/express.interface';

const router = Router();
const pool = new Pool(config.database);

interface AuthenticatedRequest extends Request {
    userData?: UserData;
}

/**
 * Register or update FCM token for the authenticated user
 * POST /api/devices/fcm-token
 */
router.post('/fcm-token', authMiddleware, async (req: AuthenticatedRequest, res: Response) => {
    try {
        const { fcm_token, device_id, user_app_version } = req.body;
        const userId = req.userData?.id;

        if (!fcm_token) {
            return res.status(400).json({
                success: false,
                message: 'FCM token is required'
            });
        }

        if (!userId) {
            return res.status(401).json({
                success: false,
                message: 'User not authenticated'
            });
        }

        // Get user reference from user ID
        const { rows: [user] } = await pool.query(`
            SELECT user_reference FROM users WHERE id = $1
        `, [userId]);

        if (!user || !user.user_reference) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        const fcmService = FCMService.getInstance();
        const success = await fcmService.registerFCMToken(
            user.user_reference,
            fcm_token,
            device_id,
            user_app_version
        );

        if (success) {
            res.json({
                success: true,
                message: 'FCM token registered successfully'
            });
        } else {
            res.status(500).json({
                success: false,
                message: 'Failed to register FCM token'
            });
        }

    } catch (error) {
        console.error('Error registering FCM token:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

/**
 * Remove FCM token for the authenticated user
 * DELETE /api/devices/fcm-token
 */
router.delete('/fcm-token', authMiddleware, async (req: AuthenticatedRequest, res: Response) => {
    try {
        const { fcm_token } = req.body;
        const userId = req.userData?.id;

        if (!fcm_token) {
            return res.status(400).json({
                success: false,
                message: 'FCM token is required'
            });
        }

        if (!userId) {
            return res.status(401).json({
                success: false,
                message: 'User not authenticated'
            });
        }

        // Get user reference from user ID
        const { rows: [user] } = await pool.query(`
            SELECT user_reference FROM users WHERE id = $1
        `, [userId]);

        if (!user || !user.user_reference) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        const fcmService = FCMService.getInstance();
        const success = await fcmService.removeFCMToken(user.user_reference, fcm_token);

        if (success) {
            res.json({
                success: true,
                message: 'FCM token removed successfully'
            });
        } else {
            res.status(500).json({
                success: false,
                message: 'Failed to remove FCM token'
            });
        }

    } catch (error) {
        console.error('Error removing FCM token:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

/**
 * Get all device details for the authenticated user
 * GET /api/devices
 */
router.get('/', authMiddleware, async (req: AuthenticatedRequest, res: Response) => {
    try {
        const userId = req.userData?.id;

        if (!userId) {
            return res.status(401).json({
                success: false,
                message: 'User not authenticated'
            });
        }

        // Get user reference from user ID
        const { rows: [user] } = await pool.query(`
            SELECT user_reference FROM users WHERE id = $1
        `, [userId]);

        if (!user || !user.user_reference) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // Get all device details for the user
        const { rows: devices } = await pool.query(`
            SELECT 
                user_device_details_id,
                device_id,
                fcm_token,
                user_app_version,
                created_at,
                updated_at
            FROM user_device_details 
            WHERE user_reference = $1
            ORDER BY created_at DESC
        `, [user.user_reference]);

        res.json({
            success: true,
            data: devices
        });

    } catch (error) {
        console.error('Error fetching device details:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

/**
 * Update device information
 * PUT /api/devices/:deviceId
 */
router.put('/:deviceId', authMiddleware, async (req: AuthenticatedRequest, res: Response) => {
    try {
        const { deviceId } = req.params;
        const { fcm_token, user_app_version } = req.body;
        const userId = req.userData?.id;

        if (!userId) {
            return res.status(401).json({
                success: false,
                message: 'User not authenticated'
            });
        }

        // Get user reference from user ID
        const { rows: [user] } = await pool.query(`
            SELECT user_reference FROM users WHERE id = $1
        `, [userId]);

        if (!user || !user.user_reference) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // Update device details
        const { rows: [updatedDevice] } = await pool.query(`
            UPDATE user_device_details 
            SET 
                fcm_token = COALESCE($1, fcm_token),
                user_app_version = COALESCE($2, user_app_version),
                updated_at = CURRENT_TIMESTAMP
            WHERE user_reference = $3 AND device_id = $4
            RETURNING *
        `, [fcm_token, user_app_version, user.user_reference, deviceId]);

        if (!updatedDevice) {
            return res.status(404).json({
                success: false,
                message: 'Device not found'
            });
        }

        res.json({
            success: true,
            message: 'Device updated successfully',
            data: {
                user_device_details_id: updatedDevice.user_device_details_id,
                device_id: updatedDevice.device_id,
                fcm_token: updatedDevice.fcm_token,
                user_app_version: updatedDevice.user_app_version,
                created_at: updatedDevice.created_at,
                updated_at: updatedDevice.updated_at
            }
        });

    } catch (error) {
        console.error('Error updating device:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

/**
 * Delete a device
 * DELETE /api/devices/:deviceId
 */
router.delete('/:deviceId', authMiddleware, async (req: AuthenticatedRequest, res: Response) => {
    try {
        const { deviceId } = req.params;
        const userId = req.userData?.id;

        if (!userId) {
            return res.status(401).json({
                success: false,
                message: 'User not authenticated'
            });
        }

        // Get user reference from user ID
        const { rows: [user] } = await pool.query(`
            SELECT user_reference FROM users WHERE id = $1
        `, [userId]);

        if (!user || !user.user_reference) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // Delete device
        const { rows: [deletedDevice] } = await pool.query(`
            DELETE FROM user_device_details 
            WHERE user_reference = $1 AND device_id = $2
            RETURNING device_id
        `, [user.user_reference, deviceId]);

        if (!deletedDevice) {
            return res.status(404).json({
                success: false,
                message: 'Device not found'
            });
        }

        res.json({
            success: true,
            message: 'Device deleted successfully'
        });

    } catch (error) {
        console.error('Error deleting device:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

export default router;
