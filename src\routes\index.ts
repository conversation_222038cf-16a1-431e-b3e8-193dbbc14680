import { Router } from 'express';
import authRoutes from './authRoutes';
import messageRoutes from './messageRoutes';
import chatRoutes from './chatRoutes';
import userRoutes from './userRoutes';
import fileRoutes from './file.routes';
import { authMiddleware } from '../middleware/auth';

const router = Router();

router.use('/auth', authRoutes);
router.use('/messages', authMiddleware, messageRoutes);
router.use('/chats', authMiddleware, chatRoutes);
router.use('/users', authMiddleware, userRoutes);
router.use('/files', authMiddleware, fileRoutes);

export default router;