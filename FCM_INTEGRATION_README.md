# FCM Push Notifications Integration

This document describes the Firebase Cloud Messaging (FCM) integration for sending push notifications to offline users when they receive messages.

## Overview

The FCM integration allows the chat-backend to send push notifications to users who are not currently connected to the WebSocket when they receive new messages. This ensures users don't miss important messages even when the app is in the background or closed.

## Architecture

### Components

1. **FCMService** (`src/services/fcmService.ts`) - Core service for FCM operations
2. **UserDeviceDetails Model** - Database table storing FCM tokens and device information
3. **Device Management API** (`src/routes/deviceRoutes.ts`) - REST endpoints for managing FCM tokens
4. **Message Service Integration** - Automatic FCM notifications for offline users

### Database Schema

```sql
-- User Device Details Table
CREATE TABLE user_device_details (
    user_device_details_id SERIAL PRIMARY KEY,
    user_reference VARCHAR(255) REFERENCES users(user_reference) ON DELETE CASCADE ON UPDATE CASCADE,
    device_id VARCHAR(30),
    fcm_token VARCHAR(300),
    user_app_version VARCHAR(10) DEFAULT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

## Setup Instructions

### 1. Firebase Project Setup

1. Create a Firebase project at https://console.firebase.google.com/
2. Enable Cloud Messaging in the Firebase console
3. Generate a service account key:
   - Go to Project Settings > Service Accounts
   - Click "Generate new private key"
   - Download the JSON file

### 2. Environment Variables

Add the following environment variables to your `.env` file:

```env
# Firebase Configuration
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour private key here\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_DATABASE_URL=https://your-project-id-default-rtdb.firebaseio.com/
```

**Important**: The private key must include the `\n` characters for line breaks.

### 3. Database Migration

Run the migration to create the user_device_details table:

```bash
# Run the migration
npm run migrate

# Or manually run the SQL from sql_migration_tracer/initial_schema.sql
```

### 4. Data Migration (Optional)

If you have existing user device details in your main project database, run the migration script:

```bash
# Install Python dependencies
cd scripts
pip install -r requirements.txt

# Set up environment variables for both databases
export SWADESIC_DB_NAME=your_main_db
export SWADESIC_DB_USER=your_main_db_user
export SWADESIC_DB_PASSWORD=your_main_db_password
export SWADESIC_DB_HOST=your_main_db_host
export SWADESIC_DB_PORT=your_main_db_port

export CHAT_DB_NAME=your_chat_db
export CHAT_DB_USER=your_chat_db_user
export CHAT_DB_PASSWORD=your_chat_db_password
export CHAT_DB_HOST=your_chat_db_host
export CHAT_DB_PORT=your_chat_db_port

# Run the migration script
python migrate_user_device_details.py
```

## API Endpoints

### Register/Update FCM Token

```http
POST /api/devices/fcm-token
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "fcm_token": "string",
  "device_id": "string",
  "user_app_version": "string"
}
```

### Remove FCM Token

```http
DELETE /api/devices/fcm-token
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "fcm_token": "string"
}
```

### Get User Devices

```http
GET /api/devices
Authorization: Bearer <jwt_token>
```

### Update Device

```http
PUT /api/devices/:deviceId
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "fcm_token": "string",
  "user_app_version": "string"
}
```

### Delete Device

```http
DELETE /api/devices/:deviceId
Authorization: Bearer <jwt_token>
```

## How It Works

### Message Flow

1. User sends a message via WebSocket or REST API
2. Message is saved to database and broadcasted to online users via WebSocket
3. System identifies offline users (not connected to WebSocket)
4. FCM notifications are sent to offline users' registered devices
5. Invalid/expired FCM tokens are automatically cleaned up

### Notification Content

- **Title**: Chat name (for group chats) or sender name (for direct chats)
- **Body**: Message content (truncated if > 100 characters)
- **Data**: Additional metadata including chat ID, message ID, sender ID, message type

### Offline Detection

Users are considered offline if they are:
- Not connected to the WebSocket server
- Not subscribed to the specific chat where the message was sent

## Testing

Run the test suite:

```bash
npm test
```

Test files:
- `src/tests/fcmService.test.ts` - FCM service unit tests
- `src/tests/deviceRoutes.test.ts` - Device API endpoint tests

## Error Handling

- Invalid FCM tokens are automatically removed from the database
- Database errors don't prevent message creation
- FCM service failures are logged but don't break the message flow
- Graceful degradation when Firebase is not configured

## Security Considerations

- FCM tokens are stored securely in the database
- API endpoints require JWT authentication
- Users can only manage their own devices
- Automatic cleanup of invalid tokens prevents token accumulation

## Monitoring

Monitor the following logs:
- FCM notification success/failure rates
- Invalid token cleanup operations
- Database connection issues
- Firebase service errors

## Troubleshooting

### Common Issues

1. **Firebase not initialized**: Check environment variables and service account key
2. **No FCM tokens found**: Ensure devices are properly registered
3. **Invalid tokens**: Tokens expire or become invalid when apps are uninstalled
4. **Database connection errors**: Check database configuration and connectivity

### Debug Logs

Enable debug logging by setting `NODE_ENV=development` to see detailed FCM operation logs.

## Performance Considerations

- FCM notifications are sent asynchronously to avoid blocking message creation
- Batch operations are used for multiple recipients
- Invalid tokens are cleaned up automatically to maintain database performance
- Connection pooling is used for database operations

## Future Enhancements

- Support for rich notifications with images
- Notification scheduling and batching
- User notification preferences
- Analytics and delivery tracking
- Support for different notification types (mentions, reactions, etc.)
