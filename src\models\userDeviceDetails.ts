export interface UserDeviceDetails {
    user_device_details_id: number;
    user_reference?: string;
    device_id?: string;
    fcm_token?: string;
    user_app_version?: string;
    created_at: Date;
    updated_at: Date;
}

export interface UserDeviceDetailsCreateDTO {
    user_reference?: string;
    device_id?: string;
    fcm_token?: string;
    user_app_version?: string;
}

export interface UserDeviceDetailsUpdateDTO {
    device_id?: string;
    fcm_token?: string;
    user_app_version?: string;
}

export interface FCMTokenRegistrationDTO {
    fcm_token: string;
    device_id?: string;
    user_app_version?: string;
}
