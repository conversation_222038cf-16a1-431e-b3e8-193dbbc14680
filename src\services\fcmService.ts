import { getMessaging } from '../config/firebase';
import { Pool } from 'pg';
import { config } from '../config/env';
import * as admin from 'firebase-admin';

export interface FCMNotificationPayload {
    title: string;
    body: string;
    data?: { [key: string]: string };
    imageUrl?: string;
}

export interface MessageNotificationData {
    chatId: string;
    messageId: string;
    senderName: string;
    messageContent: string;
    messageType: string;
    chatName?: string;
}

export class FCMService {
    private static instance: FCMService;
    private pool: Pool;
    private messaging: admin.messaging.Messaging;

    private constructor() {
        this.pool = new Pool(config.database);
        this.messaging = getMessaging();
    }

    public static getInstance(): FCMService {
        if (!FCMService.instance) {
            FCMService.instance = new FCMService();
        }
        return FCMService.instance;
    }

    /**
     * Get FCM tokens for a specific user
     */
    public async getFCMTokensForUser(userReference: string): Promise<string[]> {
        try {
            const result = await this.pool.query(`
                SELECT fcm_token 
                FROM user_device_details 
                WHERE user_reference = $1 
                AND fcm_token IS NOT NULL 
                AND fcm_token != ''
            `, [userReference]);

            return result.rows.map(row => row.fcm_token);
        } catch (error) {
            console.error(`Error fetching FCM tokens for user ${userReference}:`, error);
            return [];
        }
    }

    /**
     * Get FCM tokens for multiple users
     */
    public async getFCMTokensForUsers(userReferences: string[]): Promise<Map<string, string[]>> {
        try {
            const result = await this.pool.query(`
                SELECT user_reference, fcm_token 
                FROM user_device_details 
                WHERE user_reference = ANY($1) 
                AND fcm_token IS NOT NULL 
                AND fcm_token != ''
            `, [userReferences]);

            const tokenMap = new Map<string, string[]>();
            
            result.rows.forEach(row => {
                const userRef = row.user_reference;
                if (!tokenMap.has(userRef)) {
                    tokenMap.set(userRef, []);
                }
                tokenMap.get(userRef)!.push(row.fcm_token);
            });

            return tokenMap;
        } catch (error) {
            console.error('Error fetching FCM tokens for multiple users:', error);
            return new Map();
        }
    }

    /**
     * Send FCM notification to specific tokens
     */
    public async sendNotificationToTokens(
        tokens: string[], 
        payload: FCMNotificationPayload
    ): Promise<{ successCount: number; failureCount: number; failedTokens: string[] }> {
        if (tokens.length === 0) {
            return { successCount: 0, failureCount: 0, failedTokens: [] };
        }

        try {
            const message: admin.messaging.MulticastMessage = {
                notification: {
                    title: payload.title,
                    body: payload.body,
                    imageUrl: payload.imageUrl
                },
                data: payload.data || {},
                tokens: tokens
            };

            const response = await this.messaging.sendMulticast(message);
            
            // Collect failed tokens for cleanup
            const failedTokens: string[] = [];
            response.responses.forEach((resp, idx) => {
                if (!resp.success) {
                    failedTokens.push(tokens[idx]);
                    console.error(`Failed to send to token ${tokens[idx]}:`, resp.error);
                }
            });

            // Clean up invalid tokens
            if (failedTokens.length > 0) {
                await this.cleanupInvalidTokens(failedTokens);
            }

            console.log(`FCM notification sent: ${response.successCount} successful, ${response.failureCount} failed`);
            
            return {
                successCount: response.successCount,
                failureCount: response.failureCount,
                failedTokens
            };

        } catch (error) {
            console.error('Error sending FCM notification:', error);
            return { successCount: 0, failureCount: tokens.length, failedTokens: tokens };
        }
    }

    /**
     * Send message notification to a user
     */
    public async sendMessageNotification(
        userReference: string, 
        messageData: MessageNotificationData
    ): Promise<boolean> {
        try {
            const tokens = await this.getFCMTokensForUser(userReference);
            
            if (tokens.length === 0) {
                console.log(`No FCM tokens found for user: ${userReference}`);
                return false;
            }

            const payload: FCMNotificationPayload = {
                title: messageData.chatName || messageData.senderName,
                body: this.formatMessageBody(messageData),
                data: {
                    type: 'new_message',
                    chatId: messageData.chatId,
                    messageId: messageData.messageId,
                    senderId: userReference,
                    messageType: messageData.messageType
                }
            };

            const result = await this.sendNotificationToTokens(tokens, payload);
            return result.successCount > 0;

        } catch (error) {
            console.error(`Error sending message notification to user ${userReference}:`, error);
            return false;
        }
    }

    /**
     * Send message notifications to multiple users
     */
    public async sendMessageNotificationToUsers(
        userReferences: string[], 
        messageData: MessageNotificationData
    ): Promise<{ [userReference: string]: boolean }> {
        try {
            const tokenMap = await this.getFCMTokensForUsers(userReferences);
            const results: { [userReference: string]: boolean } = {};

            for (const userRef of userReferences) {
                const tokens = tokenMap.get(userRef) || [];
                
                if (tokens.length === 0) {
                    console.log(`No FCM tokens found for user: ${userRef}`);
                    results[userRef] = false;
                    continue;
                }

                const payload: FCMNotificationPayload = {
                    title: messageData.chatName || messageData.senderName,
                    body: this.formatMessageBody(messageData),
                    data: {
                        type: 'new_message',
                        chatId: messageData.chatId,
                        messageId: messageData.messageId,
                        senderId: userRef,
                        messageType: messageData.messageType
                    }
                };

                const result = await this.sendNotificationToTokens(tokens, payload);
                results[userRef] = result.successCount > 0;
            }

            return results;

        } catch (error) {
            console.error('Error sending message notifications to multiple users:', error);
            return userReferences.reduce((acc, userRef) => {
                acc[userRef] = false;
                return acc;
            }, {} as { [userReference: string]: boolean });
        }
    }

    /**
     * Register or update FCM token for a user
     */
    public async registerFCMToken(
        userReference: string, 
        fcmToken: string, 
        deviceId?: string, 
        userAppVersion?: string
    ): Promise<boolean> {
        try {
            // Check if device already exists
            const existingDevice = await this.pool.query(`
                SELECT user_device_details_id 
                FROM user_device_details 
                WHERE user_reference = $1 AND device_id = $2
            `, [userReference, deviceId]);

            if (existingDevice.rows.length > 0) {
                // Update existing device
                await this.pool.query(`
                    UPDATE user_device_details 
                    SET fcm_token = $1, user_app_version = $2, updated_at = CURRENT_TIMESTAMP
                    WHERE user_reference = $3 AND device_id = $4
                `, [fcmToken, userAppVersion, userReference, deviceId]);
            } else {
                // Insert new device
                await this.pool.query(`
                    INSERT INTO user_device_details 
                    (user_reference, device_id, fcm_token, user_app_version, created_at, updated_at)
                    VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                `, [userReference, deviceId, fcmToken, userAppVersion]);
            }

            console.log(`FCM token registered for user: ${userReference}`);
            return true;

        } catch (error) {
            console.error(`Error registering FCM token for user ${userReference}:`, error);
            return false;
        }
    }

    /**
     * Remove FCM token (when user logs out or uninstalls app)
     */
    public async removeFCMToken(userReference: string, fcmToken: string): Promise<boolean> {
        try {
            await this.pool.query(`
                DELETE FROM user_device_details 
                WHERE user_reference = $1 AND fcm_token = $2
            `, [userReference, fcmToken]);

            console.log(`FCM token removed for user: ${userReference}`);
            return true;

        } catch (error) {
            console.error(`Error removing FCM token for user ${userReference}:`, error);
            return false;
        }
    }

    /**
     * Clean up invalid FCM tokens from database
     */
    private async cleanupInvalidTokens(invalidTokens: string[]): Promise<void> {
        try {
            await this.pool.query(`
                DELETE FROM user_device_details 
                WHERE fcm_token = ANY($1)
            `, [invalidTokens]);

            console.log(`Cleaned up ${invalidTokens.length} invalid FCM tokens`);

        } catch (error) {
            console.error('Error cleaning up invalid FCM tokens:', error);
        }
    }

    /**
     * Format message body for notification
     */
    private formatMessageBody(messageData: MessageNotificationData): string {
        switch (messageData.messageType) {
            case 'image':
                return `${messageData.senderName} sent an image`;
            case 'file':
                return `${messageData.senderName} sent a file`;
            case 'system':
                return messageData.messageContent;
            default:
                return messageData.messageContent.length > 100 
                    ? `${messageData.messageContent.substring(0, 100)}...`
                    : messageData.messageContent;
        }
    }
}
