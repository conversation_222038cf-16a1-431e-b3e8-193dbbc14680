'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Create user_device_details table
    await queryInterface.createTable('user_device_details', {
      user_device_details_id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false
      },
      user_reference: {
        type: Sequelize.STRING(255),
        allowNull: true,
        references: {
          model: 'users',
          key: 'user_reference'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
        comment: 'Reference to user_reference in users table'
      },
      device_id: {
        type: Sequelize.STRING(30),
        allowNull: true,
        comment: 'Unique device identifier'
      },
      fcm_token: {
        type: Sequelize.STRING(300),
        allowNull: true,
        comment: 'Firebase Cloud Messaging token for push notifications'
      },
      user_app_version: {
        type: Sequelize.STRING(10),
        allowNull: true,
        defaultValue: null,
        comment: 'Version of the user app'
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Create indexes for better query performance
    await queryInterface.addIndex('user_device_details', ['user_reference'], {
      name: 'idx_user_device_details_user_reference'
    });

    await queryInterface.addIndex('user_device_details', ['device_id'], {
      name: 'idx_user_device_details_device_id'
    });

    await queryInterface.addIndex('user_device_details', ['fcm_token'], {
      name: 'idx_user_device_details_fcm_token'
    });
  },

  async down(queryInterface, Sequelize) {
    // Remove indexes first
    await queryInterface.removeIndex('user_device_details', 'idx_user_device_details_fcm_token');
    await queryInterface.removeIndex('user_device_details', 'idx_user_device_details_device_id');
    await queryInterface.removeIndex('user_device_details', 'idx_user_device_details_user_reference');

    // Drop the table
    await queryInterface.dropTable('user_device_details');
  }
};
