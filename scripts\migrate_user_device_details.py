#!/usr/bin/env python3
"""
Migration script to copy user device details from main project database
to the chat-backend database.
"""

import psycopg2
import sys
from typing import List, <PERSON><PERSON>, Optional
from decouple import config

# Configuration for main project database (source)
MAIN_DB_CONFIG = {
    'dbname': config('SWADESIC_DB_NAME'),
    'user': config('SWADESIC_DB_USER'),
    'password': config('SWADESIC_DB_PASSWORD'),
    'host': config('SWADESIC_DB_HOST'),
    'port': config('SWADESIC_DB_PORT')
}

# Configuration for chat-backend database (destination)
CHAT_DB_CONFIG = {
    'dbname': config('CHAT_DB_NAME'),
    'user': config('CHAT_DB_USER'),
    'password': config('CHAT_DB_PASSWORD'),
    'host': config('CHAT_DB_HOST'),
    'port': config('CHAT_DB_PORT')
}

def connect_to_main_db():
    """Connect to the main project database"""
    try:
        conn = psycopg2.connect(**MAIN_DB_CONFIG)
        return conn
    except Exception as e:
        print(f"Error connecting to main database: {str(e)}")
        raise

def connect_to_chat_db():
    """Connect to the chat-backend database"""
    try:
        conn = psycopg2.connect(**CHAT_DB_CONFIG)
        return conn
    except Exception as e:
        print(f"Error connecting to chat database: {str(e)}")
        raise

def fetch_user_device_details(main_conn) -> List[Tuple]:
    """Fetch user device details from main project database"""
    cursor = main_conn.cursor()
    cursor.execute("""
        SELECT 
            udd.user_device_details_id,
            u.user_reference,
            udd.device_id,
            udd.fcm_token,
            udd.user_app_version
        FROM "user"."user_device_details" udd
        INNER JOIN "user"."user" u ON udd.user_reference = u.user_reference
        WHERE udd.fcm_token IS NOT NULL 
        AND udd.fcm_token != ''
        AND u.deleted = false
        ORDER BY udd.user_device_details_id
    """)
    return cursor.fetchall()

def check_user_exists_in_chat_db(chat_conn, user_reference: str) -> bool:
    """Check if user exists in chat database"""
    cursor = chat_conn.cursor()
    cursor.execute("""
        SELECT 1 FROM users WHERE user_reference = %s LIMIT 1
    """, (user_reference,))
    return cursor.fetchone() is not None

def insert_device_details(chat_conn, device_details: Tuple) -> bool:
    """Insert device details into chat database"""
    try:
        cursor = chat_conn.cursor()
        
        # Check if device details already exist
        cursor.execute("""
            SELECT 1 FROM user_device_details 
            WHERE user_reference = %s AND device_id = %s 
            LIMIT 1
        """, (device_details[1], device_details[2]))
        
        if cursor.fetchone():
            print(f"Device details already exist for user {device_details[1]}, device {device_details[2]}")
            return False
        
        # Insert new device details
        cursor.execute("""
            INSERT INTO user_device_details 
            (user_reference, device_id, fcm_token, user_app_version, created_at, updated_at)
            VALUES (%s, %s, %s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        """, (
            device_details[1],  # user_reference
            device_details[2],  # device_id
            device_details[3],  # fcm_token
            device_details[4]   # user_app_version
        ))
        
        chat_conn.commit()
        return True
        
    except Exception as e:
        print(f"Error inserting device details for user {device_details[1]}: {str(e)}")
        chat_conn.rollback()
        return False

def main():
    """Main migration function"""
    print("Starting user device details migration...")
    
    main_conn = None
    chat_conn = None
    
    try:
        # Connect to both databases
        main_conn = connect_to_main_db()
        chat_conn = connect_to_chat_db()
        
        print("Connected to both databases successfully")
        
        # Fetch device details from main database
        device_details_list = fetch_user_device_details(main_conn)
        print(f"Found {len(device_details_list)} device details records to migrate")
        
        # Migrate each device detail record
        migrated_count = 0
        skipped_count = 0
        error_count = 0
        
        for device_details in device_details_list:
            user_reference = device_details[1]
            
            # Check if user exists in chat database
            if not check_user_exists_in_chat_db(chat_conn, user_reference):
                print(f"User {user_reference} not found in chat database, skipping...")
                skipped_count += 1
                continue
            
            # Insert device details
            if insert_device_details(chat_conn, device_details):
                migrated_count += 1
                print(f"Successfully migrated device details for user: {user_reference}")
            else:
                error_count += 1
        
        print(f"\nMigration completed!")
        print(f"Successfully migrated: {migrated_count}")
        print(f"Skipped (user not found): {skipped_count}")
        print(f"Errors: {error_count}")
        
    except Exception as e:
        print(f"Migration failed: {str(e)}")
        sys.exit(1)
        
    finally:
        if main_conn:
            main_conn.close()
        if chat_conn:
            chat_conn.close()

if __name__ == "__main__":
    main()
